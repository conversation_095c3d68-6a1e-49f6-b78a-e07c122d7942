import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../Controllers/theme_controller.dart';

class AppColors {
  // Get theme controller instance
  static ThemeController get _themeController {
    try {
      return Get.find<ThemeController>();
    } catch (e) {
      // Return a default instance if not found
      return ThemeController();
    }
  }

  // Theme-aware colors
  static Color get backgroundColor => _themeController.isDarkMode.value
      ? const Color(0xFF121212)
      : const Color(0xFFFFFFFF);

  static Color get surfaceColor => _themeController.isDarkMode.value
      ? const Color(0xFF1E1E1E)
      : const Color(0xFFFFFFFF);

  static Color get cardColor => _themeController.isDarkMode.value
      ? const Color(0xFF2C2C2C)
      : const Color(0xFFFFFFFF);

  static Color get textPrimary => _themeController.isDarkMode.value
      ? const Color(0xFFFFFFFF)
      : const Color(0xFF000000);

  static Color get textSecondary => _themeController.isDarkMode.value
      ? const Color(0xFFB3B3B3)
      : const Color(0xFF6C6C6C);

  static Color get dividerColor => _themeController.isDarkMode.value
      ? const Color(0xFF404040)
      : const Color(0xFFE0E0E0);

  // Static colors that don't change with theme
  static Color black60 = Colors.black.withOpacity(0.6);
  static Color k0xFF6C6C6C = Color(0xFF6C6C6C).withOpacity(0.4);
  static Color k1xFF403C3C = Color(0xFF403C3C).withOpacity(0.4);
  static Color k1xFFF9E005 = Color(0xFFF9E005).withOpacity(0.32);
  static Color k1xFFF0F1F1 = Color(0xFFF0F1F1).withOpacity(0.5);
  static Color k0xFF0254B8 = const Color(0xFF0254B8);
  static Color k0xFFA9ABAC = const Color(0xFFA9ABAC);
  static Color k0xFF403C3C = const Color(0xFF403C3C);
  static Color k0xFFF0F1F1 = const Color(0xFFF0F1F1);
  static Color k0xFF6C6B6B = const Color(0xFF6C6B6B);
  static Color k0xFF848484 = const Color(0xFF848484);
  static Color k0xFFF9E005 = const Color(0xFFF9E005);
  static Color k0xFF9F9F9F = const Color(0xFF9F9F9F);
  static Color k0xFFC4C4C4 = const Color(0xFFC4C4C4);
  static Color k0xFFD9D9D9 = const Color(0xFFD9D9D9);
  static Color k0xFFFB0808 = const Color(0xFFFB0808);
  static Color white = const Color(0xFFFFFFFF);
  static Color k0xFF838385 = const Color(0xFF838385);
  static Color black = const Color(0xFF000000);

  // Theme-aware versions of existing colors
  static Color get dynamicWhite => _themeController.isDarkMode.value
      ? const Color(0xFF121212)
      : const Color(0xFFFFFFFF);

  static Color get dynamicBlack => _themeController.isDarkMode.value
      ? const Color(0xFFFFFFFF)
      : const Color(0xFF000000);
}
