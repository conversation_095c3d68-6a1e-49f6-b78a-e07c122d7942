import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../view/constants/Colors.dart';

class VendorLatest extends StatelessWidget {
  const VendorLatest({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(15),
      height: 200..h,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: AppColors.cardColor,
          border: Border.all(
            color: AppColors.textSecondary.withOpacity(.1),
          ),
          boxShadow: [
            BoxShadow(
              blurRadius: 3,
              color: AppColors.textSecondary.withOpacity(.1),
            )
          ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Customer Name'.tr,
                style: TextStyle(
                    fontSize: 13..sp,
                    fontWeight: FontWeight.w400,
                    color: AppColors.k0xFFA9ABAC),
              ),
              Container(
                height: 30..h,
                width: 30..w,
                decoration: BoxDecoration(shape: BoxShape.circle),
                child: Image.asset('assets/images/profile1.png'),
              )
            ],
          ),
          Text(
            'Anton Demeron'.tr,
            style: TextStyle(
                fontSize: 17..sp,
                fontWeight: FontWeight.w600,
                color: AppColors.black),
          ),
          SizedBox(
            height: 7..h,
          ),
          Text(
            'Tracking ID: ASV23456777',
            style: TextStyle(
                fontSize: 15..sp,
                fontWeight: FontWeight.w500,
                color: AppColors.k0xFFA9ABAC),
          ),
          SizedBox(
            height: 40..h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                height: 48..h,
                width: MediaQuery.of(context).size.width * .35,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: AppColors.k0xFFFB0808),
                child: Center(
                  child: Text(
                    'Reject'.tr,
                    style: TextStyle(
                        fontSize: 15..sp,
                        fontWeight: FontWeight.w500,
                        color: AppColors.white),
                  ),
                ),
              ),
              Container(
                height: 48..h,
                width: MediaQuery.of(context).size.width * .35,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: AppColors.k0xFF0254B8),
                child: Center(
                  child: Text(
                    'Approve'.tr,
                    style: TextStyle(
                        fontSize: 15..sp,
                        fontWeight: FontWeight.w500,
                        color: AppColors.white),
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
